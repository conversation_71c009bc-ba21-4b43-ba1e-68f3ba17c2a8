using System.Collections.Concurrent;
using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DISAdmin.Core.Services;

/// <summary>
/// Služba pro detekci výpadků komunikace mezi DIS instancemi a DISApi serverem
/// </summary>
public class CommunicationOutageDetectionService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CommunicationOutageDetectionService> _logger;
    
    // In-memory cache komunikačních vzorců
    private readonly ConcurrentDictionary<int, CommunicationPattern> _patterns = new();
    private DateTime _lastAnalysisUpdate = DateTime.MinValue;
    
    // Pevná konfigurace
    private static readonly CommunicationOutageConfig Config = new()
    {
        AnalysisWindowDays = 30,
        ToleranceMinutes = 45,
        MinimumFrequencyPerHour = 0.5, // Alespoň jednou za 2 hodiny
        ExcludeWeekends = true,
        ExcludeHolidays = true,
        EscalationThresholdMinutes = 120,
        MinimumAlertInterval = 60,
        PatternUpdateIntervalHours = 6,
        CheckIntervalMinutes = 10,
        MinimumCallsForAnalysis = 150,
        MaxAnalysisIntervalMinutes = 1440
    };

    public CommunicationOutageDetectionService(
        IServiceProvider serviceProvider,
        ILogger<CommunicationOutageDetectionService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// Inicializace služby - načtení komunikačních vzorců
    /// </summary>
    public async Task InitializeAsync()
    {
        _logger.LogInformation("Inicializace detekce výpadků komunikace...");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            await UpdateCommunicationPatternsAsync(scope.ServiceProvider);
            _logger.LogInformation($"Načteno {_patterns.Count} komunikačních vzorců");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při inicializaci detekce výpadků komunikace");
            throw;
        }
    }

    /// <summary>
    /// Detekce výpadků komunikace pro všechny aktivní instance
    /// </summary>
    public async Task<List<CommunicationOutage>> DetectOutagesAsync()
    {
        try
        {
            _logger.LogDebug("Spouštím detekci výpadků komunikace");

            using var scope = _serviceProvider.CreateScope();
            var alertRuleProcessingService = scope.ServiceProvider.GetRequiredService<AlertRuleProcessingService>();

            // Kontrola, zda je globálně povolena detekce výpadků komunikace
            var hasGlobalRule = await alertRuleProcessingService.HasActiveRuleAsync("communicationOutage", null, null);
            if (!hasGlobalRule)
            {
                _logger.LogDebug("Detekce výpadků komunikace je vypnuta - žádné aktivní pravidlo");
                return new List<CommunicationOutage>();
            }

            _logger.LogDebug("Detekce výpadků komunikace je povolena");

            // Aktualizace vzorců pokud je potřeba
            var timeSinceLastUpdate = DateTime.UtcNow.Subtract(_lastAnalysisUpdate);
            if (timeSinceLastUpdate.TotalHours >= Config.PatternUpdateIntervalHours)
            {
                _logger.LogDebug("Aktualizuji komunikační vzorce (poslední aktualizace před {Hours} hodinami)",
                    timeSinceLastUpdate.TotalHours);
                await UpdateCommunicationPatternsAsync(scope.ServiceProvider);
            }
            else
            {
                _logger.LogDebug("Komunikační vzorce jsou aktuální (poslední aktualizace před {Hours} hodinami)",
                    timeSinceLastUpdate.TotalHours);
            }

            var outages = new List<CommunicationOutage>();
            var activeInstances = await GetActiveInstancesAsync(scope.ServiceProvider);

            _logger.LogDebug("Kontroluji {InstanceCount} aktivních instancí", activeInstances.Count);

            foreach (var instance in activeInstances)
            {
                _logger.LogDebug("Zpracovávám instanci {InstanceId} ({InstanceName})",
                    instance.Id, instance.Name);

                // Kontrola, zda má instance specifické pravidlo nebo použij globální
                var hasInstanceRule = await alertRuleProcessingService.HasActiveRuleAsync(
                    "communicationOutage",
                    instance.Id,
                    instance.CustomerId);

                if (hasGlobalRule || hasInstanceRule)
                {
                    _logger.LogDebug("Instance {InstanceId} má aktivní pravidlo (globální: {HasGlobal}, specifické: {HasInstance})",
                        instance.Id, hasGlobalRule, hasInstanceRule);

                    if (IsInActiveHours(instance, DateTime.UtcNow))
                    {
                        _logger.LogDebug("Instance {InstanceId} je v aktivních hodinách", instance.Id);
                        var outage = await CheckInstanceCommunicationAsync(instance, scope.ServiceProvider);
                        if (outage != null)
                        {
                            outages.Add(outage);
                        }
                    }
                    else
                    {
                        _logger.LogDebug("Instance {InstanceId} není v aktivních hodinách, přeskakuji", instance.Id);
                    }
                }
                else
                {
                    _logger.LogDebug("Instance {InstanceId} nemá aktivní pravidlo, přeskakuji", instance.Id);
                }
            }

            _logger.LogDebug("Detekce dokončena, nalezeno {OutageCount} výpadků", outages.Count);
            return outages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při detekci výpadků komunikace");
            return new List<CommunicationOutage>();
        }
    }

    /// <summary>
    /// Aktualizace komunikačních vzorců ze všech aktivních instancí
    /// </summary>
    private async Task UpdateCommunicationPatternsAsync(IServiceProvider serviceProvider)
    {
        _logger.LogInformation("Spouštím aktualizaci komunikačních vzorců");

        var context = serviceProvider.GetRequiredService<DISAdminDbContext>();
        var instances = await context.DISInstances
            .Where(i => i.Status == InstanceStatus.Active)
            .ToListAsync();

        var analysisStart = DateTime.UtcNow.AddDays(-Config.AnalysisWindowDays);

        _logger.LogDebug("Aktualizuji vzorce pro {InstanceCount} instancí (analýza od {AnalysisStart})",
            instances.Count, analysisStart);

        var updatedCount = 0;
        var errorCount = 0;

        foreach (var instance in instances)
        {
            try
            {
                _logger.LogDebug("Zpracovávám instanci {InstanceId} ({InstanceName})", instance.Id, instance.Name);
                var pattern = await AnalyzeInstancePatternAsync(instance.Id, analysisStart, serviceProvider);
                _patterns.AddOrUpdate(instance.Id, pattern, (key, oldValue) => pattern);
                updatedCount++;
                _logger.LogDebug("Úspěšně vytvořen vzorec pro instanci {InstanceId}", instance.Id);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Chyba při analýze komunikačního vzorce pro instanci {InstanceId}", instance.Id);
                errorCount++;
            }
        }

        _lastAnalysisUpdate = DateTime.UtcNow;

        _logger.LogInformation("Aktualizace komunikačních vzorců dokončena: {UpdatedCount} úspěšných, {ErrorCount} chyb, celkem vzorců v paměti: {TotalPatterns}",
            updatedCount, errorCount, _patterns.Count);
    }

    /// <summary>
    /// Analýza komunikačního vzorce konkrétní instance
    /// </summary>
    private async Task<CommunicationPattern> AnalyzeInstancePatternAsync(int instanceId, DateTime fromDate, IServiceProvider serviceProvider)
    {
        _logger.LogDebug("Analyzuji komunikační vzorec pro instanci {InstanceId} od {FromDate}", instanceId, fromDate);

        var context = serviceProvider.GetRequiredService<DISAdminDbContext>();

        // Získání informací o instanci pro rozšířené informace
        var instance = await context.DISInstances.FindAsync(instanceId);

        // Získání všech úspěšných API volání instance za analyzované období
        var apiCalls = await context.ActivityLogs
            .Where(l => l.EntityId == instanceId &&
                       l.Source == LogSource.DISApi &&
                       l.Timestamp >= fromDate &&
                       l.StatusCode < 400) // Pouze úspěšná volání
            .OrderBy(l => l.Timestamp)
            .Select(l => l.Timestamp)
            .ToListAsync();

        _logger.LogDebug("Instance {InstanceId}: nalezeno {CallsCount} API volání za období od {FromDate}",
            instanceId, apiCalls.Count, fromDate);

        // Filtrování pouze pracovních dnů (pokud je nastaveno)
        if (Config.ExcludeWeekends)
        {
            apiCalls = apiCalls.Where(t => t.DayOfWeek != DayOfWeek.Saturday &&
                                          t.DayOfWeek != DayOfWeek.Sunday).ToList();
        }

        // Analýza aktivních hodin na základě historických dat
        var activeHours = AnalyzeActiveHours(apiCalls);

        // Výpočet intervalů mezi voláními
        var intervals = new List<double>();
        for (int i = 1; i < apiCalls.Count; i++)
        {
            var interval = (apiCalls[i] - apiCalls[i - 1]).TotalMinutes;
            if (interval <= Config.MaxAnalysisIntervalMinutes) // Ignoruj intervaly delší než 24 hodin
            {
                intervals.Add(interval);
            }
        }

        // Statistické výpočty
        // Pro instance bez historických dat použij konzervativnější výchozí hodnoty
        var avgInterval = intervals.Any() ? intervals.Average() : 60; // Default 1 hodina místo 2
        var stdDev = intervals.Any() ? CalculateStandardDeviation(intervals) : 15; // Default 15 minut místo 30

        // Určení typu detekční logiky a popisu
        var (detectionType, detectionDescription, configuredWorkingHours, configuredWorkingDays) =
            GetDetectionLogicInfo(instance, activeHours);

        var pattern = new CommunicationPattern
        {
            InstanceId = instanceId,
            AnalyzedAt = DateTime.UtcNow,
            AverageIntervalMinutes = avgInterval,
            StandardDeviation = stdDev,
            MinIntervalMinutes = intervals.Any() ? intervals.Min() : 0,
            MaxIntervalMinutes = intervals.Any() ? intervals.Max() : 0,
            TotalCallsAnalyzed = apiCalls.Count,
            AnalysisPeriodDays = Config.AnalysisWindowDays,
            ActiveHours = activeHours,
            HasSufficientData = apiCalls.Count >= Config.MinimumCallsForAnalysis,
            DetectionLogicType = detectionType,
            DetectionLogicDescription = detectionDescription,
            HasConfiguredWorkingHours = instance?.WorkingHoursStart.HasValue == true && instance?.WorkingHoursEnd.HasValue == true,
            ConfiguredWorkingHours = configuredWorkingHours,
            ConfiguredWorkingDays = configuredWorkingDays
        };

        _logger.LogDebug("Vytvořen komunikační vzorec pro instanci {InstanceId}: " +
            "volání: {CallsCount}, průměr: {AvgInterval} min, std dev: {StdDev} min, " +
            "min: {MinInterval} min, max: {MaxInterval} min, dostatek dat: {HasSufficientData}, " +
            "aktivní hodiny: {ActiveHoursStart}-{ActiveHoursEnd}",
            instanceId, pattern.TotalCallsAnalyzed, pattern.AverageIntervalMinutes,
            pattern.StandardDeviation, pattern.MinIntervalMinutes, pattern.MaxIntervalMinutes,
            pattern.HasSufficientData, activeHours?.StartHour, activeHours?.EndHour);

        return pattern;
    }

    /// <summary>
    /// Kontrola komunikace konkrétní instance
    /// </summary>
    private async Task<CommunicationOutage?> CheckInstanceCommunicationAsync(DISInstance instance, IServiceProvider serviceProvider)
    {
        _logger.LogDebug("Kontrola komunikace instance {InstanceId} ({InstanceName})",
            instance.Id, instance.Name);

        if (!_patterns.TryGetValue(instance.Id, out var pattern))
        {
            _logger.LogDebug("Instance {InstanceId} - nemáme komunikační vzorec, přeskakuji", instance.Id);
            return null; // Nemáme dostatek dat pro analýzu
        }

        // Kontrola, zda má instance dostatek dat pro spolehlivou analýzu
        if (!pattern.HasSufficientData)
        {
            _logger.LogDebug("Instance {InstanceId} nemá dostatek dat pro analýzu ({CallsCount} volání, minimum: {MinCalls})",
                instance.Id, pattern.TotalCallsAnalyzed, Config.MinimumCallsForAnalysis);
            return null;
        }

        // Kontrola minimální frekvence
        var maxAllowedInterval = 60 / Config.MinimumFrequencyPerHour;
        if (pattern.AverageIntervalMinutes > maxAllowedInterval)
        {
            _logger.LogDebug("Instance {InstanceId} nekomunikuje dostatečně často pro monitoring (průměr: {AvgInterval} min, maximum: {MaxInterval} min)",
                instance.Id, pattern.AverageIntervalMinutes, maxAllowedInterval);
            return null;
        }

        var currentTime = DateTime.UtcNow;
        var currentLocalTime = currentTime.ToLocalTime();

        // Kontrola aktivních hodin podle typu konfigurace instance
        if (instance.WorkingHoursStart.HasValue && instance.WorkingHoursEnd.HasValue)
        {
            // Instance má nastavené pracovní hodiny - použij je
            if (!IsInActiveHours(instance, currentTime))
            {
                _logger.LogDebug("Instance {InstanceId} - mimo nastavené pracovní hodiny (aktuální čas: {CurrentTime}, pracovní doba: {StartTime}-{EndTime})",
                    instance.Id, currentLocalTime, instance.WorkingHoursStart, instance.WorkingHoursEnd);
                return null;
            }

            _logger.LogDebug("Instance {InstanceId} - v pracovních hodinách (aktuální čas: {CurrentTime}, pracovní doba: {StartTime}-{EndTime})",
                instance.Id, currentLocalTime, instance.WorkingHoursStart, instance.WorkingHoursEnd);
        }
        else if (pattern.ActiveHours != null)
        {
            // Instance nemá nastavené pracovní hodiny - použij historickou analýzu
            if (!pattern.ActiveHours.IsActiveTime(currentLocalTime))
            {
                _logger.LogDebug("Instance {InstanceId} - mimo aktivní hodiny podle historické analýzy (aktuální čas: {CurrentTime}, aktivní: {StartHour}-{EndHour}, dny: {ActiveDays})",
                    instance.Id, currentLocalTime, pattern.ActiveHours.StartHour, pattern.ActiveHours.EndHour,
                    string.Join(",", pattern.ActiveHours.ActiveDays));
                return null;
            }

            _logger.LogDebug("Instance {InstanceId} - v aktivních hodinách podle historické analýzy (aktuální čas: {CurrentTime}, aktivní: {StartHour}-{EndHour})",
                instance.Id, currentLocalTime, pattern.ActiveHours.StartHour, pattern.ActiveHours.EndHour);
        }
        else
        {
            _logger.LogDebug("Instance {InstanceId} - nemá nastavené pracovní hodiny ani dostatečná historická data pro analýzu aktivních hodin",
                instance.Id);
        }

        // Výpočet času od posledního kontaktu s respektováním pracovních hodin
        var timeSinceLastContact = CalculateTimeSinceLastContactInWorkingHours(instance, pattern, currentTime);

        _logger.LogDebug("Instance {InstanceId} - poslední kontakt: {LastContact}, čas od posledního kontaktu (v pracovní době): {TimeSince} min",
            instance.Id, instance.LastConnectionDate, timeSinceLastContact.TotalMinutes);

        // Získání prahu z pravidla nebo použití výchozí hodnoty
        var threshold = await GetThresholdForInstanceAsync(instance.Id, instance.CustomerId, serviceProvider);
        var expectedMaxInterval = pattern.GetExpectedMaxInterval(threshold);

        _logger.LogDebug("Instance {InstanceId} - čas od posledního kontaktu: {TimeSince} min, očekávaný max interval: {ExpectedMax} min (threshold: {Threshold} min, průměr: {Avg} min, std dev: {StdDev} min)",
            instance.Id,
            timeSinceLastContact.TotalMinutes,
            expectedMaxInterval,
            threshold,
            pattern.AverageIntervalMinutes,
            pattern.StandardDeviation);

        if (timeSinceLastContact.TotalMinutes > expectedMaxInterval)
        {
            // Kontrola, zda už nebyl alert odeslán nedávno
            var context = serviceProvider.GetRequiredService<DISAdminDbContext>();
            var recentAlert = await context.Alerts
                .Where(a => a.InstanceId == instance.Id &&
                           a.Description.Contains("nekomunikuje") &&
                           a.Timestamp > DateTime.UtcNow.AddMinutes(-Config.MinimumAlertInterval))
                .AnyAsync();

            if (!recentAlert)
            {
                _logger.LogInformation("Detekován výpadek komunikace pro instanci {InstanceId} ({InstanceName}) - {TimeSince} min > {ExpectedMax} min",
                    instance.Id, instance.Name, timeSinceLastContact.TotalMinutes, expectedMaxInterval);

                return new CommunicationOutage
                {
                    InstanceId = instance.Id,
                    InstanceName = instance.Name,
                    CustomerName = instance.Customer?.Name ?? "Neznámý",
                    CustomerAbbreviation = instance.Customer?.Abbreviation ?? "NEZ",
                    TimeSinceLastContact = timeSinceLastContact,
                    ExpectedMaxInterval = TimeSpan.FromMinutes(expectedMaxInterval),
                    Pattern = pattern,
                    LastContactTime = instance.LastConnectionDate
                };
            }
            else
            {
                _logger.LogDebug("Instance {InstanceId} - výpadek detekován, ale nedávno byl odeslán alert, přeskakuji", instance.Id);
            }
        }
        else
        {
            _logger.LogDebug("Instance {InstanceId} - komunikace v pořádku ({TimeSince} min <= {ExpectedMax} min)",
                instance.Id, timeSinceLastContact.TotalMinutes, expectedMaxInterval);
        }

        return null;
    }

    /// <summary>
    /// Získání prahu pro konkrétní instanci z pravidel
    /// </summary>
    private async Task<double> GetThresholdForInstanceAsync(int instanceId, int customerId, IServiceProvider serviceProvider)
    {
        var context = serviceProvider.GetRequiredService<DISAdminDbContext>();

        // Pokusíme se najít specifické pravidlo pro instanci
        var instanceRule = await context.AlertRules
            .Where(r => r.MetricType == "communicationOutage" &&
                       r.Enabled &&
                       r.InstanceId == instanceId)
            .FirstOrDefaultAsync();

        if (instanceRule != null)
        {
            return instanceRule.Threshold;
        }

        // Pokusíme se najít pravidlo pro zákazníka
        var customerRule = await context.AlertRules
            .Where(r => r.MetricType == "communicationOutage" &&
                       r.Enabled &&
                       r.CustomerId == customerId &&
                       r.InstanceId == null)
            .FirstOrDefaultAsync();

        if (customerRule != null)
        {
            return customerRule.Threshold;
        }

        // Globální pravidlo
        var globalRule = await context.AlertRules
            .Where(r => r.MetricType == "communicationOutage" &&
                       r.Enabled &&
                       r.CustomerId == null &&
                       r.InstanceId == null)
            .FirstOrDefaultAsync();

        return globalRule?.Threshold ?? Config.ToleranceMinutes;
    }

    /// <summary>
    /// Kontrola, zda je instance v aktivních hodinách
    /// </summary>
    private bool IsInActiveHours(DISInstance instance, DateTime checkTime)
    {
        var localTime = checkTime.ToLocalTime();
        var dayOfWeek = localTime.DayOfWeek;

        // Kontrola víkendů - pokud je globálně nastaveno vyloučení víkendů
        if (Config.ExcludeWeekends && (dayOfWeek == DayOfWeek.Saturday || dayOfWeek == DayOfWeek.Sunday))
        {
            _logger.LogDebug("Instance {InstanceId} - vyloučen víkend ({DayOfWeek})",
                instance.Id, dayOfWeek);
            return false;
        }

        // Pokud instance nemá nastavené pracovní hodiny, spoléháme se na historickou analýzu
        // Kontrola se provede až při vyhodnocování komunikačního vzorce
        if (!instance.WorkingHoursStart.HasValue || !instance.WorkingHoursEnd.HasValue)
        {
            _logger.LogDebug("Instance {InstanceId} - nemá nastavené pracovní hodiny, spoléháme se na historickou analýzu",
                instance.Id);
            return true; // Necháme rozhodnout komunikační vzorec
        }

        // Instance má nastavené vlastní pracovní hodiny
        var isWorkingDay = dayOfWeek switch
        {
            DayOfWeek.Monday => instance.WorkingDayMonday,
            DayOfWeek.Tuesday => instance.WorkingDayTuesday,
            DayOfWeek.Wednesday => instance.WorkingDayWednesday,
            DayOfWeek.Thursday => instance.WorkingDayThursday,
            DayOfWeek.Friday => instance.WorkingDayFriday,
            DayOfWeek.Saturday => instance.WorkingDaySaturday,
            DayOfWeek.Sunday => instance.WorkingDaySunday,
            _ => false
        };

        if (!isWorkingDay)
        {
            _logger.LogDebug("Instance {InstanceId} - mimo nastavené pracovní dny ({DayOfWeek})",
                instance.Id, dayOfWeek);
            return false;
        }

        // Kontrola pracovních hodin
        var currentTime = localTime.TimeOfDay;
        var startTime = instance.WorkingHoursStart.Value;
        var endTime = instance.WorkingHoursEnd.Value;

        bool isInWorkingHours;
        // Zpracování případu, kdy pracovní doba přechází přes půlnoc
        if (startTime > endTime)
        {
            isInWorkingHours = currentTime >= startTime || currentTime <= endTime;
        }
        else
        {
            isInWorkingHours = currentTime >= startTime && currentTime <= endTime;
        }

        _logger.LogDebug("Instance {InstanceId} - pracovní hodiny: {IsInHours} (aktuální čas: {CurrentTime}, rozsah: {StartTime}-{EndTime})",
            instance.Id, isInWorkingHours, currentTime, startTime, endTime);

        return isInWorkingHours;
    }

    /// <summary>
    /// Získání všech aktivních instancí
    /// </summary>
    private async Task<List<DISInstance>> GetActiveInstancesAsync(IServiceProvider serviceProvider)
    {
        var context = serviceProvider.GetRequiredService<DISAdminDbContext>();
        return await context.DISInstances
            .Include(i => i.Customer)
            .Where(i => i.Status == InstanceStatus.Active)
            .ToListAsync();
    }

    /// <summary>
    /// Výpočet standardní odchylky
    /// </summary>
    private static double CalculateStandardDeviation(List<double> values)
    {
        if (values.Count <= 1)
            return 0;

        var avg = values.Average();
        var sumOfSquares = values.Sum(x => Math.Pow(x - avg, 2));
        return Math.Sqrt(sumOfSquares / (values.Count - 1));
    }

    /// <summary>
    /// Analýza aktivních hodin na základě historických dat
    /// </summary>
    private ActiveHours? AnalyzeActiveHours(List<DateTime> apiCalls)
    {
        if (apiCalls.Count < 10) // Potřebujeme alespoň 10 volání pro analýzu
        {
            return null;
        }

        // Seskupení volání podle hodin a dnů
        var hourlyActivity = new Dictionary<int, int>(); // hodina -> počet volání
        var dailyActivity = new Dictionary<DayOfWeek, int>(); // den -> počet volání

        foreach (var call in apiCalls)
        {
            var localTime = call.ToLocalTime();
            var hour = localTime.Hour;
            var day = localTime.DayOfWeek;

            hourlyActivity[hour] = hourlyActivity.GetValueOrDefault(hour, 0) + 1;
            dailyActivity[day] = dailyActivity.GetValueOrDefault(day, 0) + 1;
        }

        // Určení aktivních dnů (dny s alespoň 10% celkové aktivity)
        var totalCalls = apiCalls.Count;
        var activeDays = dailyActivity
            .Where(kvp => kvp.Value >= totalCalls * 0.1)
            .Select(kvp => kvp.Key)
            .ToHashSet();

        if (!activeDays.Any())
        {
            return null;
        }

        // Určení aktivních hodin (hodiny s alespoň 5% celkové aktivity)
        var activeHours = hourlyActivity
            .Where(kvp => kvp.Value >= totalCalls * 0.05)
            .Select(kvp => kvp.Key)
            .OrderBy(h => h)
            .ToList();

        if (!activeHours.Any())
        {
            return null;
        }

        // Najdi nejdelší souvislý úsek aktivních hodin
        var (startHour, endHour) = FindLongestActiveSequence(activeHours);

        // Výpočet spolehlivosti na základě počtu dat a rozložení
        var confidence = Math.Min(1.0, totalCalls / 100.0); // Max spolehlivost při 100+ voláních

        return new ActiveHours
        {
            StartHour = startHour,
            EndHour = endHour,
            ActiveDays = activeDays,
            Confidence = confidence
        };
    }

    /// <summary>
    /// Najde nejdelší souvislý úsek aktivních hodin
    /// </summary>
    private (int startHour, int endHour) FindLongestActiveSequence(List<int> activeHours)
    {
        if (!activeHours.Any())
            return (0, 23);

        if (activeHours.Count == 1)
            return (activeHours[0], activeHours[0]);

        // Najdi nejdelší souvislý úsek
        var longestStart = activeHours[0];
        var longestEnd = activeHours[0];
        var longestLength = 1;

        var currentStart = activeHours[0];
        var currentLength = 1;

        for (int i = 1; i < activeHours.Count; i++)
        {
            if (activeHours[i] == activeHours[i - 1] + 1)
            {
                // Souvislý úsek pokračuje
                currentLength++;
            }
            else
            {
                // Konec souvislého úseku
                if (currentLength > longestLength)
                {
                    longestStart = currentStart;
                    longestEnd = activeHours[i - 1];
                    longestLength = currentLength;
                }
                currentStart = activeHours[i];
                currentLength = 1;
            }
        }

        // Kontrola posledního úseku
        if (currentLength > longestLength)
        {
            longestStart = currentStart;
            longestEnd = activeHours.Last();
        }

        return (longestStart, longestEnd);
    }

    /// <summary>
    /// Získání informací o použité detekční logice
    /// </summary>
    private (string detectionType, string detectionDescription, string? configuredWorkingHours, string? configuredWorkingDays)
        GetDetectionLogicInfo(DISInstance? instance, ActiveHours? activeHours)
    {
        if (instance == null)
        {
            return ("Neznámý", "Instance nebyla nalezena", null, null);
        }

        var hasWorkingHours = instance.WorkingHoursStart.HasValue && instance.WorkingHoursEnd.HasValue;

        if (hasWorkingHours)
        {
            // Instance má nastavené pracovní hodiny
            var workingHours = $"{instance.WorkingHoursStart:hh\\:mm} - {instance.WorkingHoursEnd:hh\\:mm}";
            var workingDays = GetWorkingDaysDescription(instance);

            var description = $"Používají se nastavené pracovní hodiny ({workingHours}) pro dny: {workingDays}. " +
                            "Detekce výpadků se provádí pouze v těchto hodinách.";

            return ("Nastavené pracovní hodiny", description, workingHours, workingDays);
        }
        else if (activeHours != null)
        {
            // Instance používá historickou analýzu
            var confidence = (activeHours.Confidence * 100).ToString("F0");
            var activeDaysStr = string.Join(", ", activeHours.ActiveDays.Select(d => GetDayName(d)));

            var description = $"Používá se historická analýza komunikace. " +
                            $"Aktivní hodiny: {activeHours.StartHour:D2}:00 - {activeHours.EndHour:D2}:00, " +
                            $"aktivní dny: {activeDaysStr}. Spolehlivost: {confidence}%.";

            return ("Historická analýza", description, $"{activeHours.StartHour:D2}:00 - {activeHours.EndHour:D2}:00", activeDaysStr);
        }
        else
        {
            // Instance nemá ani nastavené pracovní hodiny ani dostatečná historická data
            var description = "Instance nemá nastavené pracovní hodiny ani dostatečná historická data pro analýzu aktivních hodin. " +
                            "Detekce výpadků se provádí nepřetržitě.";

            return ("Nepřetržitý monitoring", description, null, null);
        }
    }

    /// <summary>
    /// Získání popisu pracovních dnů
    /// </summary>
    private string GetWorkingDaysDescription(DISInstance instance)
    {
        var days = new List<string>();

        if (instance.WorkingDayMonday) days.Add("Po");
        if (instance.WorkingDayTuesday) days.Add("Út");
        if (instance.WorkingDayWednesday) days.Add("St");
        if (instance.WorkingDayThursday) days.Add("Čt");
        if (instance.WorkingDayFriday) days.Add("Pá");
        if (instance.WorkingDaySaturday) days.Add("So");
        if (instance.WorkingDaySunday) days.Add("Ne");

        return days.Any() ? string.Join(", ", days) : "Žádné";
    }

    /// <summary>
    /// Získání českého názvu dne
    /// </summary>
    private string GetDayName(DayOfWeek day)
    {
        return day switch
        {
            DayOfWeek.Monday => "Po",
            DayOfWeek.Tuesday => "Út",
            DayOfWeek.Wednesday => "St",
            DayOfWeek.Thursday => "Čt",
            DayOfWeek.Friday => "Pá",
            DayOfWeek.Saturday => "So",
            DayOfWeek.Sunday => "Ne",
            _ => day.ToString()
        };
    }

    /// <summary>
    /// Výpočet času od posledního kontaktu s respektováním pracovních hodin
    /// </summary>
    private TimeSpan CalculateTimeSinceLastContactInWorkingHours(DISInstance instance, CommunicationPattern pattern, DateTime currentTime)
    {
        var lastContact = instance.LastConnectionDate;
        if (!lastContact.HasValue)
        {
            return TimeSpan.MaxValue;
        }

        var currentLocalTime = currentTime.ToLocalTime();
        var lastContactLocalTime = lastContact.Value.ToLocalTime();

        // Kontrola, zda instance má explicitně nastavené pracovní hodiny
        if (instance.WorkingHoursStart.HasValue && instance.WorkingHoursEnd.HasValue)
        {
            return CalculateWithExplicitWorkingHours(instance, currentLocalTime, lastContactLocalTime, currentTime, lastContact.Value);
        }

        // Instance nemá explicitně nastavené pracovní hodiny - zkus použít historickou analýzu
        if (pattern.ActiveHours != null)
        {
            return CalculateWithHistoricalActiveHours(instance, pattern.ActiveHours, currentLocalTime, lastContactLocalTime, currentTime, lastContact.Value);
        }

        // Instance nemá ani explicitní pracovní hodiny ani historická data - použij standardní výpočet
        _logger.LogDebug("Instance {InstanceId} - nemá nastavené pracovní hodiny ani historická data, používám standardní výpočet",
            instance.Id);
        return currentTime - lastContact.Value;
    }

    /// <summary>
    /// Výpočet času s explicitně nastavenými pracovními hodinami
    /// </summary>
    private TimeSpan CalculateWithExplicitWorkingHours(DISInstance instance, DateTime currentLocalTime, DateTime lastContactLocalTime, DateTime currentTime, DateTime lastContact)
    {
        var workStart = instance.WorkingHoursStart!.Value;
        var workEnd = instance.WorkingHoursEnd!.Value;

        // Pokud je poslední kontakt v aktuální pracovní době, použij standardní výpočet
        if (IsTimeInWorkingHours(lastContactLocalTime, instance) &&
            IsSameWorkingDay(lastContactLocalTime, currentLocalTime, instance))
        {
            return currentTime - lastContact;
        }

        // Poslední kontakt byl mimo aktuální pracovní dobu
        // Najdi začátek aktuální pracovní doby
        var todayWorkStart = currentLocalTime.Date.Add(workStart);

        // Pokud je aktuální čas před začátkem pracovní doby, vrať 0 (ještě nezačala pracovní doba)
        if (currentLocalTime < todayWorkStart)
        {
            _logger.LogDebug("Instance {InstanceId} - aktuální čas ({CurrentTime}) je před začátkem pracovní doby ({WorkStart})",
                instance.Id, currentLocalTime, todayWorkStart);
            return TimeSpan.Zero;
        }

        // Počítej od začátku aktuální pracovní doby
        var timeSinceWorkStart = currentLocalTime - todayWorkStart;

        _logger.LogDebug("Instance {InstanceId} - poslední kontakt mimo aktuální pracovní dobu ({LastContact}), počítám od začátku pracovní doby ({WorkStart}), čas: {TimeSince} min",
            instance.Id, lastContactLocalTime, todayWorkStart, timeSinceWorkStart.TotalMinutes);

        return timeSinceWorkStart;
    }

    /// <summary>
    /// Výpočet času s historicky analyzovanými aktivními hodinami
    /// </summary>
    private TimeSpan CalculateWithHistoricalActiveHours(DISInstance instance, ActiveHours activeHours, DateTime currentLocalTime, DateTime lastContactLocalTime, DateTime currentTime, DateTime lastContact)
    {
        // Pokud je poslední kontakt v aktuální aktivní době, použij standardní výpočet
        if (activeHours.IsActiveTime(lastContactLocalTime) &&
            IsSameActiveDay(lastContactLocalTime, currentLocalTime, activeHours))
        {
            return currentTime - lastContact;
        }

        // Poslední kontakt byl mimo aktuální aktivní dobu
        // Najdi začátek aktuální aktivní doby
        var todayActiveStart = currentLocalTime.Date.AddHours(activeHours.StartHour);

        // Pokud je aktuální čas před začátkem aktivní doby, vrať 0 (ještě nezačala aktivní doba)
        if (currentLocalTime < todayActiveStart)
        {
            _logger.LogDebug("Instance {InstanceId} - aktuální čas ({CurrentTime}) je před začátkem aktivní doby ({ActiveStart}) podle historické analýzy",
                instance.Id, currentLocalTime, todayActiveStart);
            return TimeSpan.Zero;
        }

        // Počítej od začátku aktuální aktivní doby
        var timeSinceActiveStart = currentLocalTime - todayActiveStart;

        _logger.LogDebug("Instance {InstanceId} - poslední kontakt mimo aktuální aktivní dobu ({LastContact}), počítám od začátku aktivní doby ({ActiveStart}) podle historické analýzy, čas: {TimeSince} min",
            instance.Id, lastContactLocalTime, todayActiveStart, timeSinceActiveStart.TotalMinutes);

        return timeSinceActiveStart;
    }

    /// <summary>
    /// Kontrola, zda jsou dva časy ve stejném aktivním dni podle historické analýzy
    /// </summary>
    private bool IsSameActiveDay(DateTime time1, DateTime time2, ActiveHours activeHours)
    {
        // Pokud jsou ve stejném kalendářním dni a oba jsou v aktivních dnech, považuj za stejný aktivní den
        if (time1.Date != time2.Date)
            return false;

        return activeHours.ActiveDays.Contains(time1.DayOfWeek) && activeHours.ActiveDays.Contains(time2.DayOfWeek);
    }

    /// <summary>
    /// Kontrola, zda je čas v pracovních hodinách
    /// </summary>
    private bool IsTimeInWorkingHours(DateTime localTime, DISInstance instance)
    {
        if (!instance.WorkingHoursStart.HasValue || !instance.WorkingHoursEnd.HasValue)
            return true;

        var timeOfDay = localTime.TimeOfDay;
        var startTime = instance.WorkingHoursStart.Value;
        var endTime = instance.WorkingHoursEnd.Value;

        // Zpracování případu, kdy pracovní doba přechází přes půlnoc
        if (startTime > endTime)
        {
            return timeOfDay >= startTime || timeOfDay <= endTime;
        }
        else
        {
            return timeOfDay >= startTime && timeOfDay <= endTime;
        }
    }

    /// <summary>
    /// Kontrola, zda jsou dva časy ve stejném pracovním dni
    /// </summary>
    private bool IsSameWorkingDay(DateTime time1, DateTime time2, DISInstance instance)
    {
        // Pro jednoduchost - pokud jsou ve stejném kalendářním dni, považuj za stejný pracovní den
        // V budoucnu by se mohlo rozšířit o složitější logiku pro směny přes půlnoc
        return time1.Date == time2.Date;
    }

    /// <summary>
    /// Získání konfigurace služby
    /// </summary>
    public CommunicationOutageConfig GetConfiguration()
    {
        return Config;
    }

    /// <summary>
    /// Získání komunikačního vzorce pro instanci
    /// </summary>
    public CommunicationPattern? GetPatternForInstance(int instanceId)
    {
        _patterns.TryGetValue(instanceId, out var pattern);
        return pattern;
    }

    /// <summary>
    /// Získání všech načtených komunikačních vzorců
    /// </summary>
    public Dictionary<int, CommunicationPattern> GetAllPatterns()
    {
        return new Dictionary<int, CommunicationPattern>(_patterns);
    }
}
